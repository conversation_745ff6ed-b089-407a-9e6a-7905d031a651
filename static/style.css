/* Custom styles for MCP Blog Generator */

/* Smooth transitions */
* {
    transition: all 0.2s ease-in-out;
}

/* Custom gradient backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Glass morphism effect */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-1px);
}

/* Loading animation */
.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(0,0,0,0);
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    40% {
        color: black;
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    60% {
        text-shadow:
            .25em 0 0 black,
            .5em 0 0 rgba(0,0,0,0);
    }
    80%, 100% {
        text-shadow:
            .25em 0 0 black,
            .5em 0 0 black;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Responsive text sizes */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .card-title {
        font-size: 1.25rem;
    }
}

/* Print styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .dropdown,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white !important;
    }
}

/* Toast notifications */
.toast {
    z-index: 9999;
}

/* Form enhancements */
.form-control:focus-within .label-text {
    color: hsl(var(--p));
}

/* Steps component enhancement */
.steps .step-content {
    margin-top: 0.5rem;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Code blocks in blog content */
.prose pre {
    background: #f4f4f4;
    border-radius: 8px;
    padding: 1rem;
    overflow-x: auto;
}

.prose code {
    background: #f4f4f4;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Blog content styling */
.prose h1, .prose h2, .prose h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.prose ul, .prose ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.prose blockquote {
    border-left: 4px solid hsl(var(--p));
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background: rgba(0, 0, 0, 0.05);
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}
