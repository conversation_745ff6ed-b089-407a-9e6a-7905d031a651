{% extends "base.html" %}

{% block title %}Error - MCP Blog Generator{% endblock %}

{% block content %}
<div class="hero min-h-96">
    <div class="hero-content text-center">
        <div class="max-w-md">
            <i class="fas fa-exclamation-triangle text-6xl text-error mb-4"></i>
            <h1 class="text-5xl font-bold">Oops!</h1>
            <p class="py-6">{{ error_message or "Something went wrong while generating your blog post." }}</p>
            
            <!-- Error Details (collapsible) -->
            {% if error_details %}
            <div class="collapse collapse-arrow bg-base-200 mb-6">
                <input type="checkbox" /> 
                <div class="collapse-title text-xl font-medium">
                    Technical Details
                </div>
                <div class="collapse-content"> 
                    <pre class="text-left text-sm">{{ error_details }}</pre>
                </div>
            </div>
            {% endif %}
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home mr-2"></i>
                    Go Home
                </a>
                <button onclick="history.back()" class="btn btn-outline">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Go Back
                </button>
            </div>
            
            <!-- Troubleshooting Tips -->
            <div class="mt-8 text-left">
                <h3 class="text-lg font-semibold mb-4">Troubleshooting Tips:</h3>
                <ul class="list-disc list-inside space-y-2 text-sm">
                    <li>Check your internet connection</li>
                    <li>Ensure API keys are properly configured</li>
                    <li>Try a different topic or simpler request</li>
                    <li>Wait a moment and try again</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
