{% extends "base.html" %}

{% block title %}Home - MCP Blog Generator{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero min-h-96 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mb-8">
    <div class="hero-content text-center text-white">
        <div class="max-w-md">
            <h1 class="mb-5 text-5xl font-bold">
                <i class="fas fa-magic mr-3"></i>
                Blog Generator
            </h1>
            <p class="mb-5">
                Generate high-quality blog posts using AI-powered research and writing. 
                Powered by MCP (Model Context Protocol) for intelligent content creation.
            </p>
        </div>
    </div>
</div>

<!-- Blog Generation Form -->
<div class="card bg-base-100 shadow-xl">
    <div class="card-body">
        <h2 class="card-title text-2xl mb-6">
            <i class="fas fa-edit mr-2"></i>
            Create Your Blog Post
        </h2>
        
        <form action="/research" method="post" class="space-y-6">
            <!-- Blog Topic Input -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text text-lg font-semibold">Blog Topic</span>
                    <span class="label-text-alt">Required</span>
                </label>
                <textarea 
                    name="topic" 
                    class="textarea textarea-bordered h-24 text-base" 
                    placeholder="Enter your blog topic here... (e.g., 'The Future of Artificial Intelligence in Healthcare')"
                    required
                ></textarea>
                <label class="label">
                    <span class="label-text-alt">Describe what you want to write about</span>
                </label>
            </div>

            <!-- Research Depth Selection -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text text-lg font-semibold">Research Depth</span>
                </label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="cursor-pointer">
                        <input type="radio" name="depth" value="quick" class="radio radio-primary" />
                        <div class="card bg-base-200 p-4 ml-3">
                            <h3 class="font-bold">Quick</h3>
                            <p class="text-sm">Fast research (1-2 minutes)</p>
                        </div>
                    </label>
                    <label class="cursor-pointer">
                        <input type="radio" name="depth" value="medium" class="radio radio-primary" checked />
                        <div class="card bg-base-200 p-4 ml-3">
                            <h3 class="font-bold">Medium</h3>
                            <p class="text-sm">Balanced research (3-5 minutes)</p>
                        </div>
                    </label>
                    <label class="cursor-pointer">
                        <input type="radio" name="depth" value="deep" class="radio radio-primary" />
                        <div class="card bg-base-200 p-4 ml-3">
                            <h3 class="font-bold">Deep</h3>
                            <p class="text-sm">Comprehensive research (5-10 minutes)</p>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-control mt-8">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket mr-2"></i>
                    Generate Blog Post
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Features Section -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body text-center">
            <i class="fas fa-search text-4xl text-primary mb-4"></i>
            <h3 class="card-title justify-center">AI-Powered Research</h3>
            <p>Deep web search using Linkup MCP for comprehensive information gathering</p>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body text-center">
            <i class="fas fa-brain text-4xl text-secondary mb-4"></i>
            <h3 class="card-title justify-center">Intelligent Writing</h3>
            <p>Advanced AI models for professional, engaging content creation</p>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body text-center">
            <i class="fas fa-download text-4xl text-accent mb-4"></i>
            <h3 class="card-title justify-center">Easy Export</h3>
            <p>Download your blog posts in multiple formats for immediate use</p>
        </div>
    </div>
</div>

<!-- How It Works -->
<div class="mt-12">
    <h2 class="text-3xl font-bold text-center mb-8">How It Works</h2>
    <div class="steps steps-vertical lg:steps-horizontal">
        <div class="step step-primary">
            <div class="step-content">
                <h3 class="font-bold">Enter Topic</h3>
                <p>Describe what you want to write about</p>
            </div>
        </div>
        <div class="step step-primary">
            <div class="step-content">
                <h3 class="font-bold">AI Research</h3>
                <p>Our AI conducts comprehensive research</p>
            </div>
        </div>
        <div class="step step-primary">
            <div class="step-content">
                <h3 class="font-bold">Content Generation</h3>
                <p>Professional blog post is created</p>
            </div>
        </div>
        <div class="step step-primary">
            <div class="step-content">
                <h3 class="font-bold">Download & Use</h3>
                <p>Get your ready-to-publish content</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Form validation and enhancement
    document.querySelector('form').addEventListener('submit', function(e) {
        const topic = document.querySelector('textarea[name="topic"]').value.trim();
        if (!topic) {
            e.preventDefault();
            alert('Please enter a blog topic');
            return;
        }
        
        // Show loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';
        submitBtn.disabled = true;
    });
</script>
{% endblock %}
