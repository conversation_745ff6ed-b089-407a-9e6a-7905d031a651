{% extends "base.html" %}

{% block title %}Results - MCP Blog Generator{% endblock %}

{% block content %}
<!-- Success Alert -->
<div class="alert alert-success mb-6">
    <i class="fas fa-check-circle"></i>
    <span>Blog post generated successfully!</span>
</div>

<!-- Results Header -->
<div class="flex flex-col lg:flex-row gap-6">
    <!-- Main Content -->
    <div class="flex-1">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="card-title text-2xl">
                        <i class="fas fa-file-alt mr-2"></i>
                        Generated Blog Post
                    </h2>
                    <div class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                            <li><a href="#" onclick="copyToClipboard()"><i class="fas fa-copy mr-2"></i>Copy to Clipboard</a></li>
                            <li><a href="#" onclick="window.print()"><i class="fas fa-print mr-2"></i>Print</a></li>
                            <li><a href="/download/{{ filename }}" download><i class="fas fa-download mr-2"></i>Download</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Blog Content -->
                <div class="prose max-w-none" id="blog-content">
                    {{ blog_content | safe }}
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4 mt-6">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Create Another Post
            </a>
            <a href="/download/{{ filename }}" class="btn btn-secondary" download>
                <i class="fas fa-download mr-2"></i>
                Download Markdown
            </a>
            <button onclick="copyToClipboard()" class="btn btn-outline">
                <i class="fas fa-copy mr-2"></i>
                Copy Content
            </button>
            <button onclick="window.print()" class="btn btn-outline">
                <i class="fas fa-print mr-2"></i>
                Print
            </button>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="w-full lg:w-80">
        <!-- Blog Statistics -->
        <div class="card bg-base-100 shadow-lg mb-6">
            <div class="card-body">
                <h3 class="card-title text-lg">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Statistics
                </h3>
                <div class="stats stats-vertical shadow">
                    <div class="stat">
                        <div class="stat-title">Word Count</div>
                        <div class="stat-value text-primary" id="word-count">-</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">Reading Time</div>
                        <div class="stat-value text-secondary" id="reading-time">-</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">File Size</div>
                        <div class="stat-value text-accent">{{ file_size }}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Research Info -->
        {% if search_results %}
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <h3 class="card-title text-lg">
                    <i class="fas fa-search mr-2"></i>
                    Research Sources
                </h3>
                <p class="text-sm text-base-content/70 mb-4">
                    Based on {{ search_results | length }} sources
                </p>
                <button class="btn btn-outline btn-sm w-full" onclick="showSearchResults()">
                    <i class="fas fa-eye mr-2"></i>
                    View Sources
                </button>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Search Results Modal -->
{% if search_results %}
<dialog id="search_modal" class="modal">
    <div class="modal-box w-11/12 max-w-5xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="fas fa-search mr-2"></i>
            Research Sources
        </h3>
        <div class="space-y-4 max-h-96 overflow-y-auto">
            {% for result in search_results %}
            <div class="card bg-base-200">
                <div class="card-body p-4">
                    <h4 class="font-semibold">{{ result.title or 'Source ' + loop.index }}</h4>
                    {% if result.url %}
                    <a href="{{ result.url }}" target="_blank" class="text-primary text-sm hover:underline">
                        {{ result.url }}
                    </a>
                    {% endif %}
                    {% if result.snippet %}
                    <p class="text-sm text-base-content/70 mt-2">{{ result.snippet[:200] }}...</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn">Close</button>
            </form>
        </div>
    </div>
</dialog>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    // Calculate and display statistics
    function calculateStats() {
        const content = document.getElementById('blog-content').innerText;
        const words = content.trim().split(/\s+/).length;
        const readingTime = Math.ceil(words / 200); // Average reading speed: 200 words per minute
        
        document.getElementById('word-count').textContent = words.toLocaleString();
        document.getElementById('reading-time').textContent = readingTime + ' min';
    }
    
    // Copy content to clipboard
    function copyToClipboard() {
        const content = document.getElementById('blog-content').innerText;
        navigator.clipboard.writeText(content).then(function() {
            // Show success toast
            const toast = document.createElement('div');
            toast.className = 'toast toast-top toast-end';
            toast.innerHTML = '<div class="alert alert-success"><span>Content copied to clipboard!</span></div>';
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        });
    }
    
    // Show search results modal
    function showSearchResults() {
        document.getElementById('search_modal').showModal();
    }
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        calculateStats();
    });
</script>
{% endblock %}
