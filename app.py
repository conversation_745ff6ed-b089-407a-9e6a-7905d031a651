from fastapi import <PERSON><PERSON><PERSON>, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import asyncio
import os
from datetime import datetime
import json
from dotenv import load_dotenv
from langchain_openai import Chat<PERSON>penAI
from langchain_groq import <PERSON>tGroq
from mcp_use import MCPAgent, MCPClient

load_dotenv()

app = FastAPI(title="MCP Blog Generator", version="1.0.0")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Global variable to store current blog results
current_blog = None

# Initialize MCP components
def get_mcp_agent():
    """Initialize MCP agent with OpenAI model"""
    llm = ChatOpenAI(
        model="gpt-4o-mini",
        temperature=0.7,
        api_key=os.getenv("OPAPIKEY")
    )
    # llm = ChatGroq(
    #     model="llama-3.3-70b-versatile",
    #     api_key=os.getenv("GROQ_API_KEY")
    # )
    client = MCPClient.from_config_file("multiserver_setup_config.json")
    agent = MCPAgent(
        llm=llm,
        client=client,
        use_server_manager=False,
        max_steps=30
    )
    return agent

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page with blog topic form"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/research")
async def perform_research(
    request: Request,
    topic: str = Form(...),
    depth: str = Form(default="medium")
):
    """Perform research and generate blog post"""
    global current_blog
    try:
        agent = get_mcp_agent()

        # Step 1: Perform web search
        print(f"🔍 Performing {depth} research for: {topic}")
        search_result = await agent.run(
            f"Use the 'linkup' server to search for comprehensive information about: {topic}. "
            f"Search depth: {depth}. Gather relevant facts, statistics, and insights."
        )

        # Step 2: Generate blog post using Groq
        print(f"📝 Generating blog post for: {topic}")
        llm = ChatGroq(
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            api_key=os.getenv("GROQ_API_KEY")
        )

        blog_prompt = f"""
        Write a comprehensive, engaging blog post about: {topic}

        Based on the research: {search_result}

        Requirements:
        - Write in a professional yet engaging tone
        - Include relevant statistics and facts from the research
        - Structure with clear headings and subheadings
        - Add practical insights and actionable advice
        - Use markdown formatting
        - Aim for 800-1200 words
        - Include a compelling introduction and conclusion

        Make it informative, well-researched, and valuable to readers.
        """

        blog_post = await llm.ainvoke(blog_prompt)

        if blog_post and search_result:
            # Step 3: Save blog post to filestore
            print(f"💾 Saving blog post to filestore...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"blog_{safe_topic.replace(' ', '_').lower()}_{timestamp}.md"

            save_result = await agent.run(
                f"Use the 'write_file' tool from the 'filesystem' server to save a file named '{filename}' "
                f"in the filestore directory with this content:\n\n{blog_post.content}"
            )

            # Calculate file size
            file_path = f"filestore/{filename}"
            file_size = "Unknown"
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                if size_bytes < 1024:
                    file_size = f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    file_size = f"{size_bytes / 1024:.1f} KB"
                else:
                    file_size = f"{size_bytes / (1024 * 1024):.1f} MB"

            result = {
                "topic": topic,
                "depth": depth,
                "search_results": search_result,
                "blog_content": blog_post.content,
                "filename": filename,
                "file_size": file_size,
                "save_result": save_result
            }
            current_blog = result

            return templates.TemplateResponse(
                "results.html",
                {
                    "request": request,
                    "blog_content": blog_post.content,
                    "filename": filename,
                    "file_size": file_size,
                    "search_results": [{"title": "Research Data", "content": str(search_result)}],
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Research or blog generation failed")
    except Exception as e:
        print(f"Error: {str(e)}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "Failed to generate blog post",
                "error_details": str(e)
            }
        )

@app.get("/results")
async def view_results(request: Request):
    global current_blog
    if not current_blog:
        return RedirectResponse(url="/", status_code=303)
    return templates.TemplateResponse(
        "results.html",
        {
            "request": request,
            "blog_content": current_blog.get("blog_content", ""),
            "filename": current_blog.get("filename", ""),
            "file_size": current_blog.get("file_size", "Unknown"),
            "search_results": [{"title": "Research Data", "content": str(current_blog.get("search_results", ""))}],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    )

@app.get("/download/{filename}")
async def download_file(filename: str):
    file_path = f"filestore/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    from fastapi.responses import FileResponse
    return FileResponse(file_path, filename=filename)

@app.get("/api/status")
async def api_status():
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "has_blog": current_blog is not None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000) 