from fastapi import FastAP<PERSON>, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
import asyncio
import os
from datetime import datetime
import json
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq

load_dotenv()

app = FastAPI(title="MCP Blog Generator", version="1.0.0")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Global variable to store current blog results
current_blog = None

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page with blog topic form"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/research")
async def perform_research(
    request: Request,
    topic: str = Form(...),
    depth: str = Form(default="medium")
):
    """Perform research and generate blog post"""
    global current_blog
    try:
        print(f"🔍 Generating blog post for: {topic} (depth: {depth})")
        
        # Check if we have the required API keys
        groq_api_key = os.getenv("GROQ_API_KEY")
        openai_api_key = os.getenv("OPAPIKEY")
        
        if not groq_api_key and not openai_api_key:
            raise Exception("No API keys found. Please set GROQ_API_KEY or OPAPIKEY in your .env file")
        
        # Use Groq if available, otherwise OpenAI
        if groq_api_key:
            print("📝 Using Groq for blog generation...")
            llm = ChatGroq(
                model="llama-3.3-70b-versatile",
                temperature=0.7,
                api_key=groq_api_key
            )
        else:
            print("📝 Using OpenAI for blog generation...")
            llm = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.7,
                api_key=openai_api_key
            )
        
        # Create a comprehensive blog prompt based on the topic and depth
        research_instructions = {
            "quick": "Focus on the main points and key insights",
            "medium": "Include detailed analysis and practical examples", 
            "deep": "Provide comprehensive coverage with statistics, case studies, and expert insights"
        }
        
        word_targets = {
            "quick": "400-600",
            "medium": "800-1200", 
            "deep": "1200-1800"
        }
        
        blog_prompt = f"""
        Write a comprehensive, engaging blog post about: {topic}
        
        Research depth: {depth} - {research_instructions.get(depth, research_instructions['medium'])}
        
        Requirements:
        - Write in a professional yet engaging tone
        - Structure with clear headings and subheadings using markdown
        - Include practical insights and actionable advice
        - Add relevant examples and use cases
        - Use markdown formatting (# ## ### for headers, **bold**, *italic*, etc.)
        - Aim for {word_targets.get(depth, word_targets['medium'])} words
        - Include a compelling introduction and conclusion
        - Add bullet points and numbered lists where appropriate
        - Make it informative and valuable to readers
        - Include relevant statistics and trends (you can use general knowledge)
        
        Format the response in clean markdown that's ready for publication.
        """
        
        blog_post = await llm.ainvoke(blog_prompt)
        
        if blog_post:
            # Save blog post to filestore directory
            print(f"💾 Saving blog post to filestore...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"blog_{safe_topic.replace(' ', '_').lower()}_{timestamp}.md"
            
            # Ensure filestore directory exists
            os.makedirs("filestore", exist_ok=True)
            
            # Save the file
            file_path = f"filestore/{filename}"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(blog_post.content)
            
            # Calculate file size
            file_size = "Unknown"
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                if size_bytes < 1024:
                    file_size = f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    file_size = f"{size_bytes / 1024:.1f} KB"
                else:
                    file_size = f"{size_bytes / (1024 * 1024):.1f} MB"
            
            result = {
                "topic": topic,
                "depth": depth,
                "blog_content": blog_post.content,
                "filename": filename,
                "file_size": file_size
            }
            current_blog = result
            
            print(f"✅ Blog post saved successfully: {filename}")
            
            return templates.TemplateResponse(
                "results.html",
                {
                    "request": request,
                    "blog_content": blog_post.content,
                    "filename": filename,
                    "file_size": file_size,
                    "search_results": [{"title": "AI Generated Content", "content": f"Generated using {depth} research depth"}],
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            )
        else:
            raise Exception("Blog generation failed - no content returned")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "Failed to generate blog post",
                "error_details": str(e)
            }
        )

@app.get("/results")
async def view_results(request: Request):
    global current_blog
    if not current_blog:
        return RedirectResponse(url="/", status_code=303)
    return templates.TemplateResponse(
        "results.html",
        {
            "request": request,
            "blog_content": current_blog.get("blog_content", ""),
            "filename": current_blog.get("filename", ""),
            "file_size": current_blog.get("file_size", "Unknown"),
            "search_results": [{"title": "AI Generated Content", "content": f"Generated using {current_blog.get('depth', 'medium')} research depth"}],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    )

@app.get("/download/{filename}")
async def download_file(filename: str):
    file_path = f"filestore/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(file_path, filename=filename)

@app.get("/api/status")
async def api_status():
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "has_blog": current_blog is not None,
        "groq_configured": bool(os.getenv("GROQ_API_KEY")),
        "openai_configured": bool(os.getenv("OPAPIKEY"))
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
